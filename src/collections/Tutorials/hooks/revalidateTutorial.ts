import type { CollectionA<PERSON><PERSON>hangeHook } from 'payload'

import { revalidatePath, revalidateTag } from 'next/cache'

import type { Tutorial } from '../../../payload-types'

export const revalidatePage: CollectionAfterChangeHook<Tutorial> = ({
  doc,
  previousDoc,
  req: { payload, context },
}) => {
  if (!context.disableRevalidate) {
    if (doc._status === 'published') {
      // Get category slug for new URL format
      const categorySlug =
        typeof doc.category === 'object' && doc.category?.slug ? doc.category.slug : 'general'
      const path = `/tutorial/${categorySlug}/${doc.slug}`

      payload.logger.info(`Revalidating tutorial at path: ${path}`)

      revalidatePath(path)
      revalidateTag(`tutorial_${doc.slug}`)

      // Also revalidate old path format for backward compatibility
      const oldPath = `/tutorials/${doc.slug}`
      revalidatePath(oldPath)
    }

    // If the tutorial was previously published, but now is draft, revalidate the old path
    if (previousDoc?._status === 'published' && doc._status !== 'published') {
      const categorySlug =
        typeof previousDoc.category === 'object' && previousDoc.category?.slug
          ? previousDoc.category.slug
          : 'general'
      const oldPath = `/tutorial/${categorySlug}/${previousDoc.slug}`

      payload.logger.info(`Revalidating old tutorial path: ${oldPath}`)

      revalidatePath(oldPath)
      revalidateTag(`tutorial_${previousDoc.slug}`)

      // Also revalidate old path format for backward compatibility
      const legacyPath = `/tutorials/${previousDoc.slug}`
      revalidatePath(legacyPath)
    }
  }

  return doc
}

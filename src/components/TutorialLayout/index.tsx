'use client'

import React from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  ChevronLeft,
  ChevronRight,
  Clock,
  BookOpen,
  FileText,
  Home,
  Search,
  Menu,
  X,
  Code,
  Palette,
  Trophy,
} from 'lucide-react'
import MarkdownRichText from '@/components/MarkdownRichText'
import { AdminEditButton } from '@/components/AdminEditButton'
import { Logo } from '@/components/Logo/Logo'

interface Tutorial {
  id: string
  title: string
  excerpt?: string
  content: any
  category: {
    title: string
    slug: string
    color?: string
  }
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime?: string
  previousTutorial?: {
    title: string
    slug: string
  }
  nextTutorial?: {
    title: string
    slug: string
  }
  relatedTutorials?: Array<{
    title: string
    slug: string
    excerpt?: string
    categorySlug?: string
  }>
}

interface TutorialSidebarItem {
  title: string
  slug: string
  isActive?: boolean
  isChapter?: boolean
  categorySlug?: string
  children?: TutorialSidebarItem[]
}

interface TutorialLayoutProps {
  tutorial: Tutorial
  sidebarItems: TutorialSidebarItem[]
  previousLesson?: TutorialSidebarItem | null
  nextLesson?: TutorialSidebarItem | null
  isAdmin?: boolean
  editUrl?: string
}

const difficultyColors = {
  beginner:
    'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 border border-green-200 dark:border-green-800',
  intermediate:
    'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300 border border-amber-200 dark:border-amber-800',
  advanced:
    'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 border border-red-200 dark:border-red-800',
}

const CodeEditor: React.FC<{
  code: string
  language: string
  title: string
  tryItYourself: boolean
  expectedOutput?: string
}> = ({ code, language, title, tryItYourself, expectedOutput }) => {
  const [userCode, setUserCode] = React.useState(code)
  const [showOutput, setShowOutput] = React.useState(false)

  return (
    <div className="my-6">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-lg font-semibold">{title}</h4>
        {tryItYourself && (
          <Button
            onClick={() => setShowOutput(!showOutput)}
            size="sm"
            className="bg-green-600 hover:bg-green-700"
          >
            Try it Yourself
          </Button>
        )}
      </div>

      <div className="border rounded-lg overflow-hidden">
        <div className="bg-gray-800 text-white p-3 text-sm font-mono">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-300">{language.toUpperCase()}</span>
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
          </div>
          {tryItYourself ? (
            <textarea
              value={userCode}
              onChange={(e) => setUserCode(e.target.value)}
              className="w-full bg-transparent border-none outline-none resize-none font-mono text-sm"
              rows={code.split('\n').length}
            />
          ) : (
            <pre className="text-green-400 whitespace-pre-wrap">{code}</pre>
          )}
        </div>

        {tryItYourself && showOutput && expectedOutput && (
          <div className="bg-gray-100 dark:bg-gray-700 p-3 border-t">
            <div className="text-sm font-semibold mb-2">Output:</div>
            <pre className="text-sm">{expectedOutput}</pre>
          </div>
        )}
      </div>
    </div>
  )
}

// Tutorial Info Section Component (to be used below the main header)
const TutorialInfoSection: React.FC<{
  tutorial: Tutorial
  isAdmin?: boolean
  editUrl?: string
}> = ({ tutorial, isAdmin, editUrl }) => {
  return (
    <div className="bg-gradient-to-r from-brand-50 via-white to-brand-50 dark:from-gray-800 dark:via-gray-750 dark:to-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Tutorial Badges and Meta Info */}
            <div className="flex flex-wrap items-center gap-3 mb-4">
              {tutorial.category && typeof tutorial.category === 'object' && (
                <Badge
                  variant="secondary"
                  className="px-4 py-2 text-sm font-semibold rounded-full shadow-sm"
                  style={{
                    backgroundColor: (tutorial.category as any).color
                      ? (tutorial.category as any).color + '15'
                      : '#3B82F6' + '15',
                    color: (tutorial.category as any).color || '#3B82F6',
                    border: `1px solid ${(tutorial.category as any).color || '#3B82F6'}30`,
                  }}
                >
                  {(tutorial.category as any).title}
                </Badge>
              )}
              {tutorial?.difficulty && (
                <Badge
                  className={`px-4 py-2 text-sm font-semibold rounded-full shadow-sm ${
                    difficultyColors[tutorial.difficulty as keyof typeof difficultyColors] ||
                    difficultyColors.beginner
                  }`}
                >
                  {tutorial.difficulty}
                </Badge>
              )}
              {tutorial.estimatedTime && (
                <div className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-700 rounded-full text-sm text-gray-700 dark:text-gray-300 shadow-sm border border-gray-200 dark:border-gray-600">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">{tutorial.estimatedTime}</span>
                </div>
              )}
            </div>
          </div>

          {/* Admin Edit Button */}
          {isAdmin && editUrl && (
            <div className="ml-4 flex-shrink-0">
              <AdminEditButton editUrl={editUrl} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

const Sidebar: React.FC<{
  items: TutorialSidebarItem[]
  isOpen?: boolean
  onClose?: () => void
}> = ({ items, isOpen = true, onClose }) => {
  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40" onClick={onClose} />
      )}

      {/* Sidebar */}
      <div
        className={`
        fixed lg:static inset-y-0 left-0 z-50 lg:z-auto
        w-80 bg-white dark:bg-gray-900
        border-r border-gray-200 dark:border-gray-700
        transform transition-all duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        overflow-y-auto shadow-xl lg:shadow-sm
        backdrop-blur-sm lg:backdrop-blur-none
      `}
      >
        {/* Sidebar Header */}
        <div className="bg-gradient-to-r from-brand-600 to-brand-700 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <BookOpen className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-bold text-lg">Tutorial Contents</h3>
                <p className="text-brand-100 text-sm">Learn step by step</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="lg:hidden text-white hover:bg-white/20 p-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="text-brand-100">Progress</span>
              <span className="text-white font-medium">
                {Math.round(
                  (items.filter(
                    (item) =>
                      item.isActive ||
                      (item.children && item.children.some((child) => child.isActive)),
                  ).length /
                    items.length) *
                    100,
                )}
                %
              </span>
            </div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <div
                className="bg-white rounded-full h-2 transition-all duration-300"
                style={{
                  width: `${Math.round((items.filter((item) => item.isActive || (item.children && item.children.some((child) => child.isActive))).length / items.length) * 100)}%`,
                }}
              />
            </div>
          </div>
        </div>

        {/* Navigation Content */}
        <div className="p-4">
          <nav className="space-y-1">
            {items.map((item, index) => (
              <div key={index}>
                {item.isChapter ? (
                  // Modern Chapter header
                  <div className="mb-4">
                    <div className="px-4 py-3 text-sm font-bold text-gray-900 dark:text-white bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-750 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
                      <div className="flex items-center gap-3">
                        <div className="p-1.5 bg-brand-100 dark:bg-brand-900 rounded-lg">
                          <FileText className="h-3.5 w-3.5 text-brand-600 dark:text-brand-400" />
                        </div>
                        <span className="font-semibold">{item.title}</span>
                      </div>
                    </div>
                    {item.children && (
                      <div className="mt-3 space-y-1">
                        {item.children.map((child, childIndex) => (
                          <Link
                            key={childIndex}
                            href={`/tutorial/${child.categorySlug || 'general'}/${child.slug}`}
                            className={`group flex items-center gap-3 px-4 py-3 rounded-lg text-sm transition-all duration-200 ${
                              child.isActive
                                ? 'bg-brand-600 text-white shadow-md font-medium'
                                : 'text-gray-700 dark:text-gray-300 hover:bg-brand-50 dark:hover:bg-gray-800 hover:text-brand-700 dark:hover:text-brand-300'
                            }`}
                          >
                            <div className="flex items-center gap-3 flex-1 min-w-0">
                              <div className="flex-shrink-0">
                                <div
                                  className={`w-2 h-2 rounded-full transition-colors ${
                                    child.isActive
                                      ? 'bg-white'
                                      : 'bg-gray-300 dark:bg-gray-600 group-hover:bg-brand-500'
                                  }`}
                                />
                              </div>
                              <span className="truncate font-medium">{child.title}</span>
                            </div>
                            {child.isActive && (
                              <div className="flex-shrink-0">
                                <div className="w-1.5 h-1.5 bg-white rounded-full" />
                              </div>
                            )}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  // Modern Regular tutorial item
                  <Link
                    href={`/tutorial/${item.categorySlug || 'general'}/${item.slug}`}
                    className={`group flex items-center gap-3 px-4 py-3 rounded-lg text-sm transition-all duration-200 ${
                      item.isActive
                        ? 'bg-brand-600 text-white shadow-md font-medium'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-brand-50 dark:hover:bg-gray-800 hover:text-brand-700 dark:hover:text-brand-300'
                    }`}
                  >
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <div className="flex-shrink-0">
                        <div
                          className={`w-2 h-2 rounded-full transition-colors ${
                            item.isActive
                              ? 'bg-white'
                              : 'bg-gray-300 dark:bg-gray-600 group-hover:bg-brand-500'
                          }`}
                        />
                      </div>
                      <span className="truncate font-medium">{item.title}</span>
                    </div>
                    {item.isActive && (
                      <div className="flex-shrink-0">
                        <div className="w-1.5 h-1.5 bg-white rounded-full" />
                      </div>
                    )}
                  </Link>
                )}
              </div>
            ))}
          </nav>
        </div>
      </div>
    </>
  )
}

export const TutorialLayout: React.FC<TutorialLayoutProps> = ({
  tutorial,
  sidebarItems,
  previousLesson,
  nextLesson,
  isAdmin = false,
  editUrl,
}) => {
  const [sidebarOpen, setSidebarOpen] = React.useState(false)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Tutorial Info Section (below the main header) */}
      <TutorialInfoSection tutorial={tutorial} isAdmin={isAdmin} editUrl={editUrl} />

      <div className="flex">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-20 left-4 z-50">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="bg-white dark:bg-gray-800 shadow-lg border-gray-200 dark:border-gray-700"
          >
            {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>
        </div>

        {/* Modern Sidebar */}
        <Sidebar items={sidebarItems} isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

        {/* Main Content */}
        <div className="flex-1 lg:ml-0">
          <div className="max-w-5xl mx-auto p-6 lg:p-8">
            {/* Modern Breadcrumb */}
            <nav className="mb-6">
              <div className="flex items-center space-x-2 text-sm bg-white dark:bg-gray-800 rounded-lg px-4 py-3 border border-gray-200 dark:border-gray-700">
                <Link
                  href="/"
                  className="flex items-center gap-1.5 text-gray-600 dark:text-gray-400 hover:text-brand-600 dark:hover:text-brand-400 transition-colors font-medium"
                >
                  <Home className="h-4 w-4" />
                  Home
                </Link>
                <ChevronRight className="h-3 w-3 text-gray-400" />
                <Link
                  href="/tutorials"
                  className="text-gray-600 dark:text-gray-400 hover:text-brand-600 dark:hover:text-brand-400 transition-colors font-medium"
                >
                  Tutorials
                </Link>
                <ChevronRight className="h-3 w-3 text-gray-400" />
                <span className="text-gray-900 dark:text-gray-100 font-semibold">
                  {(tutorial.category as any).title}
                </span>
              </div>
            </nav>

            {/* Tutorial Content Article */}
            <article className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8 mb-10 tutorial-card">
              {/* Tutorial Content */}
              <div className="prose dark:prose-invert max-w-none prose-lg prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-a:text-brand-600 dark:prose-a:text-brand-400 prose-code:text-brand-600 dark:prose-code:text-brand-400 prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-pre:bg-gray-900 prose-pre:border prose-pre:border-gray-200 dark:prose-pre:border-gray-700 tutorial-content">
                {tutorial?.content ? (
                  <MarkdownRichText
                    data={tutorial.content}
                    enableGutter={false}
                    tutorialTitle={tutorial.title}
                  />
                ) : (
                  <div className="text-center py-12">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 italic text-lg">
                      No content available for this tutorial.
                    </p>
                  </div>
                )}
              </div>
            </article>

            {/* Enhanced Lesson Navigation */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-10">
              <div className="flex flex-col sm:flex-row gap-4 justify-between">
                {/* Previous Lesson Button */}
                {previousLesson ? (
                  <Link
                    href={`/tutorial/${previousLesson.categorySlug || 'general'}/${previousLesson.slug}`}
                    className="group flex-1 max-w-md"
                  >
                    <div className="flex items-center gap-3 p-4 rounded-lg border-2 border-gray-300 hover:border-blue-400 bg-gray-100 hover:bg-white transition-all duration-200 hover:shadow-md">
                      <div className="flex-shrink-0 p-2 bg-gray-300 rounded-lg group-hover:bg-blue-100 transition-colors">
                        <ChevronLeft className="h-4 w-4 text-gray-700 group-hover:text-blue-600" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-1">
                          Previous
                        </div>
                        <div className="font-semibold text-gray-900 group-hover:text-blue-700 transition-colors text-sm leading-tight truncate">
                          {previousLesson.title}
                        </div>
                      </div>
                    </div>
                  </Link>
                ) : (
                  <div className="flex-1 max-w-md"></div>
                )}

                {/* Next Lesson Button */}
                {nextLesson ? (
                  <Link
                    href={`/tutorial/${nextLesson.categorySlug || 'general'}/${nextLesson.slug}`}
                    className="group flex-1 max-w-md"
                  >
                    <div className="flex items-center gap-3 p-4 rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200 shadow-md hover:shadow-lg">
                      <div className="min-w-0 flex-1 text-right">
                        <div className="text-xs font-semibold text-blue-100 uppercase tracking-wide mb-1">
                          Next
                        </div>
                        <div className="font-semibold text-white text-sm leading-tight truncate">
                          {nextLesson.title}
                        </div>
                      </div>
                      <div className="flex-shrink-0 p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
                        <ChevronRight className="h-4 w-4 text-white" />
                      </div>
                    </div>
                  </Link>
                ) : (
                  <div className="flex-1 max-w-md"></div>
                )}
              </div>

              {/* Progress Indicator */}
              {(previousLesson || nextLesson) && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                    <span>Navigate between lessons</span>
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-brand-600 rounded-full"></div>
                      <div className="w-2 h-2 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                      <div className="w-2 h-2 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Related Tutorials */}
            {tutorial.relatedTutorials && tutorial.relatedTutorials.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8 tutorial-card">
                <div className="flex items-center gap-2 mb-6">
                  <BookOpen className="h-5 w-5 text-brand-600" />
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Related Tutorials
                  </h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {tutorial.relatedTutorials.map((related, index) => (
                    <Link
                      key={index}
                      href={`/tutorial/${related.categorySlug || 'general'}/${related.slug}`}
                      className="group"
                    >
                      <Card className="h-full border border-gray-200 dark:border-gray-600 hover:border-brand-300 dark:hover:border-brand-600 hover:shadow-lg transition-all duration-200 group-hover:scale-[1.02]">
                        <CardHeader className="p-6">
                          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-brand-700 dark:group-hover:text-brand-300 transition-colors">
                            {related.title}
                          </CardTitle>
                          {related.excerpt && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed mt-2">
                              {related.excerpt}
                            </p>
                          )}
                        </CardHeader>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

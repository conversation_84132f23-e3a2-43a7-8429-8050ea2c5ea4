import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import Link from 'next/link'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BookOpen, Clock, Target, Users, Play, CheckCircle, ArrowRight } from 'lucide-react'
import { getDifficultyColor } from '@/config/branding'

interface CategoryPageProps {
  params: Promise<{
    slug: string
  }>
}

interface Tutorial {
  id: string
  title: string
  slug: string
  lessonNumber?: number
}

interface Chapter {
  id: string
  title: string
  description?: string
  slug: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  order: number
  estimatedTime?: string
  icon?: string
  tutorials: Tutorial[]
  isCompleted?: boolean
}

interface Category {
  id: string
  title: string
  description?: string
  color?: string
  chapters: Chapter[]
  totalTutorials: number
  totalTime: string
}

const difficultyColors = {
  beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
}

async function getCategoryWithChapters(categorySlug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    // Get category
    const categories = await payload.find({
      collection: 'categories',
      where: {
        slug: {
          equals: categorySlug,
        },
      },
      limit: 1,
    })

    if (categories.docs.length === 0) {
      return null
    }

    const category = categories.docs[0]

    if (!category) {
      return null
    }

    // Get chapters for this category
    const chapters = await payload.find({
      collection: 'chapters',
      where: {
        category: {
          equals: category.id,
        },
        _status: {
          equals: 'published',
        },
      },
      sort: 'order',
      limit: 100,
    })

    // Get tutorials for each chapter
    const chaptersWithTutorials = await Promise.all(
      chapters.docs.map(async (chapter: any) => {
        const tutorials = await payload.find({
          collection: 'tutorials',
          where: {
            chapter: {
              equals: chapter.id,
            },
            _status: {
              equals: 'published',
            },
          },
          sort: 'lessonNumber',
          limit: 1000,
          pagination: false,
        })

        return {
          id: chapter.id,
          title: chapter.title,
          description: chapter.description,
          slug: chapter.slug,
          difficulty: chapter.difficulty,
          order: chapter.order,
          estimatedTime: chapter.estimatedTime,
          icon: chapter.icon,
          tutorials: tutorials.docs.map((tutorial: any) => ({
            id: tutorial.id,
            title: tutorial.title,
            slug: tutorial.slug,
            lessonNumber: tutorial.lessonNumber || 0,
          })),
          isCompleted: false, // This would come from user progress
        }
      }),
    )

    // Get total tutorials for category
    const allTutorials = await payload.find({
      collection: 'tutorials',
      where: {
        category: {
          equals: category.id,
        },
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
    })

    return {
      id: category.id,
      title: category.title,
      description: category.description,
      color: category.color || '#3B82F6',
      chapters: chaptersWithTutorials,
      totalTutorials: allTutorials.totalDocs,
      totalTime: '10+ hours', // Calculate based on chapter times
    }
  } catch (error) {
    console.error('Error fetching category with chapters:', error)
    return null
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { slug: categorySlug } = await params
  const categoryData = await getCategoryWithChapters(categorySlug)

  if (!categoryData) {
    notFound()
  }

  const completedChapters = categoryData.chapters.filter((c) => c.isCompleted).length
  const totalChapters = categoryData.chapters.length
  const progressPercentage = totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div
              className="p-4 rounded-full"
              style={{ backgroundColor: (categoryData.color || '#3B82F6') + '20' }}
            >
              <BookOpen className="h-12 w-12" style={{ color: categoryData.color || '#3B82F6' }} />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            {categoryData.title} Course
          </h1>
          {categoryData.description && (
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              {categoryData.description}
            </p>
          )}

          {/* Course Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <BookOpen className="h-6 w-6 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {totalChapters}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Chapters</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Target className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {categoryData.totalTutorials}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Lessons</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Clock className="h-6 w-6 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {categoryData.totalTime}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Duration</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Users className="h-6 w-6 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">Free</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Access</div>
            </div>
          </div>
        </div>

        {/* Progress Overview */}
        {totalChapters > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Your Progress</CardTitle>
              <CardDescription>
                Track your learning journey through the {categoryData.title} course
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {completedChapters} of {totalChapters} chapters completed
                </span>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {Math.round(progressPercentage)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  className="h-3 rounded-full transition-all duration-300"
                  style={{
                    width: `${progressPercentage}%`,
                    backgroundColor: categoryData.color || '#3B82F6',
                  }}
                ></div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Chapters and Lessons List */}
        <div className="space-y-8">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Course Content</h2>
            <Badge variant="outline">{totalChapters} chapters</Badge>
          </div>

          {categoryData.chapters.length > 0 ? (
            <div className="space-y-8">
              {categoryData.chapters.map((chapter) => (
                <div key={chapter.id} className="space-y-4">
                  {/* Chapter Header */}
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm"
                      style={{ backgroundColor: categoryData.color || '#3B82F6' }}
                    >
                      {chapter.order}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                      Chapter {chapter.order}: {chapter.title}
                    </h3>
                    <Badge
                      className={
                        difficultyColors[chapter.difficulty as keyof typeof difficultyColors] ||
                        difficultyColors.beginner
                      }
                    >
                      {chapter.difficulty}
                    </Badge>
                  </div>

                  {/* Chapter Description */}
                  {chapter.description && (
                    <p className="text-gray-600 dark:text-gray-400 ml-11">{chapter.description}</p>
                  )}

                  {/* Lessons List */}
                  {chapter.tutorials.length > 0 ? (
                    <div className="ml-11 space-y-2">
                      {chapter.tutorials.map((tutorial) => (
                        <Link
                          key={tutorial.id}
                          href={`/tutorial/${course.category?.slug || 'general'}/${tutorial.slug}`}
                          className="block group"
                        >
                          <div className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <div className="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600 group-hover:bg-blue-500 transition-colors"></div>
                            <span className="text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                              {tutorial.title}
                            </span>
                            <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-500 opacity-0 group-hover:opacity-100 transition-all" />
                          </div>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="ml-11 text-gray-500 dark:text-gray-400 italic">
                      No lessons available yet
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No chapters available yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                We&apos;re working on creating comprehensive chapters for {categoryData.title}.
                Check back soon!
              </p>
              <Button asChild>
                <Link href="/tutorials">Browse All Tutorials</Link>
              </Button>
            </div>
          )}
        </div>

        {/* Call to Action */}
        {categoryData.chapters.length > 0 && (
          <div className="mt-16 text-center">
            <Card
              className="border-2"
              style={{ borderColor: (categoryData.color || '#3B82F6') + '40' }}
            >
              <CardContent className="p-8">
                <div
                  className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4"
                  style={{ backgroundColor: (categoryData.color || '#3B82F6') + '20' }}
                >
                  <Play className="h-8 w-8" style={{ color: categoryData.color || '#3B82F6' }} />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Ready to Start Learning {categoryData.title}?
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                  Begin your journey with Chapter 1 and work your way through our structured
                  curriculum.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    size="lg"
                    style={{ backgroundColor: categoryData.color || '#3B82F6' }}
                    asChild
                  >
                    <Link
                      href={
                        categoryData.chapters[0]?.tutorials[0]?.slug
                          ? `/tutorials/${categoryData.chapters[0].tutorials[0].slug}`
                          : `/chapters/${categoryData.chapters[0]?.slug}`
                      }
                    >
                      <Play className="h-5 w-5 mr-2" />
                      Start Chapter 1
                    </Link>
                  </Button>
                  <Button variant="outline" size="lg" asChild>
                    <Link href="/tutorials">
                      <BookOpen className="h-5 w-5 mr-2" />
                      Browse All Tutorials
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug: categorySlug } = await params
  const categoryData = await getCategoryWithChapters(categorySlug)

  if (!categoryData) {
    return {
      title: 'Course Not Found | GuruDevs',
    }
  }

  return {
    title: `${categoryData.title} Course | GuruDevs`,
    description:
      categoryData.description ||
      `Learn ${categoryData.title} programming with our comprehensive course.`,
    openGraph: {
      title: `${categoryData.title} Course`,
      description:
        categoryData.description ||
        `Learn ${categoryData.title} programming with our comprehensive course.`,
      type: 'website',
    },
  }
}

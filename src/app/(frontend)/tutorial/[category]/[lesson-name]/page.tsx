import type { <PERSON>ada<PERSON> } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'

import { TutorialLayout } from '@/components/TutorialLayout'
import { getMeUser } from '@/utilities/getMeUser'

interface TutorialPageProps {
  params: Promise<{
    category: string
    'lesson-name': string
  }>
}

async function getTutorial(categorySlug: string, lessonSlug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    // First, find the category by slug
    const categories = await payload.find({
      collection: 'categories',
      where: {
        slug: {
          equals: categorySlug,
        },
      },
      limit: 1,
    })

    if (categories.docs.length === 0) {
      return null
    }

    const category = categories.docs[0]

    // Then find the tutorial by lesson slug and category
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        and: [
          {
            slug: {
              equals: lessonSlug,
            },
          },
          {
            category: {
              equals: category.id,
            },
          },
          {
            _status: {
              equals: 'published',
            },
          },
        ],
      },
      limit: 1,
      depth: 2,
      select: {
        title: true,
        slug: true,
        excerpt: true,
        content: true,
        category: true,
        chapter: true,
        lessonNumber: true,
        difficulty: true,
        estimatedTime: true,
        previousTutorial: true,
        nextTutorial: true,
        relatedTutorials: true,
      },
    })

    if (tutorials.docs.length === 0) {
      return null
    }

    const tutorial = tutorials.docs[0]

    // Debug logging
    console.log('Fetched tutorial:', {
      id: tutorial?.id,
      title: tutorial?.title,
      hasContent: !!tutorial?.content,
      contentType: typeof tutorial?.content,
      contentKeys: tutorial?.content ? Object.keys(tutorial.content) : 'No content',
    })

    // Get all chapters for this category to build sidebar
    const chapters = await payload.find({
      collection: 'chapters',
      where: {
        category: {
          equals: category.id,
        },
      },
      sort: 'order',
      limit: 50,
    })

    // Then, get all tutorials for each chapter
    const chaptersWithTutorials = await Promise.all(
      chapters.docs.map(async (chapter: any) => {
        const chapterTutorials = await payload.find({
          collection: 'tutorials',
          where: {
            chapter: {
              equals: chapter.id,
            },
            _status: {
              equals: 'published',
            },
          },
          sort: 'lessonNumber',
          limit: 50,
          select: {
            title: true,
            slug: true,
            lessonNumber: true,
            category: true,
          },
        })

        return {
          id: chapter.id,
          title: chapter.title,
          slug: chapter.slug,
          order: chapter.order,
          tutorials: chapterTutorials.docs.map((t: any) => ({
            title: t.title,
            slug: t.slug,
            lessonNumber: t.lessonNumber || 1,
            isActive: t.slug === lessonSlug,
            categorySlug: categorySlug, // Add category slug for URL generation
          })),
        }
      }),
    )

    // Create sidebar items with chapter-based structure
    const sidebarItems = []

    // Add chapters with their tutorials
    if (chaptersWithTutorials.length > 0) {
      chaptersWithTutorials
        .sort((a, b) => a.order - b.order)
        .forEach((chapter) => {
          sidebarItems.push({
            title: `Chapter ${chapter.order}: ${chapter.title}`,
            slug: chapter.slug,
            isChapter: true,
            children: chapter.tutorials.map((tutorial) => ({
              title: `${tutorial.lessonNumber}. ${tutorial.title}`,
              slug: tutorial.slug,
              isActive: tutorial.isActive,
              categorySlug: tutorial.categorySlug, // Include category slug
            })),
          })
        })
    }

    // Calculate next and previous lessons for navigation
    let previousLesson = null
    let nextLesson = null

    // Find current tutorial position and get adjacent lessons
    for (const chapter of chaptersWithTutorials) {
      for (let i = 0; i < chapter.tutorials.length; i++) {
        const currentTutorial = chapter.tutorials[i]
        if (currentTutorial.slug === lessonSlug) {
          // Get previous lesson
          if (i > 0) {
            const prev = chapter.tutorials[i - 1]
            previousLesson = {
              title: prev.title,
              slug: prev.slug,
              categorySlug: categorySlug,
            }
          } else {
            // Look in previous chapter
            const currentChapterIndex = chaptersWithTutorials.findIndex((c) => c.id === chapter.id)
            if (currentChapterIndex > 0) {
              const prevChapter = chaptersWithTutorials[currentChapterIndex - 1]
              if (prevChapter.tutorials.length > 0) {
                const lastTutorial = prevChapter.tutorials[prevChapter.tutorials.length - 1]
                previousLesson = {
                  title: lastTutorial.title,
                  slug: lastTutorial.slug,
                  categorySlug: categorySlug,
                }
              }
            }
          }

          // Get next lesson
          if (i < chapter.tutorials.length - 1) {
            const next = chapter.tutorials[i + 1]
            nextLesson = {
              title: next.title,
              slug: next.slug,
              categorySlug: categorySlug,
            }
          } else {
            // Look in next chapter
            const currentChapterIndex = chaptersWithTutorials.findIndex((c) => c.id === chapter.id)
            if (currentChapterIndex < chaptersWithTutorials.length - 1) {
              const nextChapter = chaptersWithTutorials[currentChapterIndex + 1]
              if (nextChapter.tutorials.length > 0) {
                const firstTutorial = nextChapter.tutorials[0]
                nextLesson = {
                  title: firstTutorial.title,
                  slug: firstTutorial.slug,
                  categorySlug: categorySlug,
                }
              }
            }
          }

          break
        }
      }
      if (previousLesson !== null || nextLesson !== null) break
    }

    return {
      tutorial,
      sidebarItems,
      previousLesson,
      nextLesson,
      category,
    }
  } catch (error) {
    console.error('Error fetching tutorial:', error)
    return null
  }
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })

  try {
    // Get all published tutorials with their categories
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
        category: true,
      },
      depth: 1,
    })

    return tutorials.docs.map((tutorial: any) => ({
      category: tutorial.category?.slug || 'general',
      'lesson-name': tutorial.slug,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export async function generateMetadata({ params }: TutorialPageProps): Promise<Metadata> {
  const { category, 'lesson-name': lessonName } = await params
  const data = await getTutorial(category, lessonName)

  if (!data?.tutorial) {
    return {
      title: 'Tutorial Not Found',
    }
  }

  const { tutorial } = data

  return {
    title: tutorial.title,
    description: tutorial.excerpt || `Learn ${tutorial.title}`,
  }
}

export default async function TutorialPage({ params }: TutorialPageProps) {
  const { category, 'lesson-name': lessonName } = await params
  const data = await getTutorial(category, lessonName)

  if (!data) {
    notFound()
  }

  const { tutorial, sidebarItems, previousLesson, nextLesson } = data

  // Get current user for admin check
  let isAdmin = false
  try {
    const { user } = await getMeUser({
      nullUserRedirect: false,
    })
    isAdmin = user?.role === 'admin'
  } catch (error) {
    // User is not authenticated, isAdmin remains false
    console.log('User not authenticated:', error)
    isAdmin = false
  }

  // Generate edit URL
  const editUrl = tutorial?.id
    ? `/admin/collections/tutorials/${tutorial.id}`
    : '/admin/collections/tutorials/create'

  console.log('Edit URL generated:', editUrl, 'Tutorial ID:', tutorial?.id)

  // Transform tutorial data to match TutorialLayout interface
  const tutorialData = {
    id: tutorial?.id || '',
    title: tutorial?.title || '',
    excerpt: tutorial?.excerpt || '',
    content: tutorial?.content || null,
    category:
      tutorial?.category && typeof tutorial.category === 'object'
        ? {
            title: (tutorial.category as any).title || 'General',
            slug: (tutorial.category as any).slug || 'general',
            color: (tutorial.category as any).color || '#3B82F6',
          }
        : {
            title: 'General',
            slug: 'general',
            color: '#3B82F6',
          },
    difficulty: tutorial?.difficulty || 'beginner',
    estimatedTime: tutorial?.estimatedTime || null,
    relatedTutorials: tutorial?.relatedTutorials
      ? (tutorial.relatedTutorials as any[]).map((related) => ({
          title: related.title || 'Related Tutorial',
          slug: related.slug || '',
          excerpt: related.excerpt || '',
          categorySlug: category, // Add category slug for URL generation
        }))
      : [],
  }

  return (
    <TutorialLayout
      tutorial={tutorialData}
      sidebarItems={sidebarItems}
      previousLesson={previousLesson}
      nextLesson={nextLesson}
      isAdmin={isAdmin}
      editUrl={editUrl}
    />
  )
}
